package com.xq.tmall.controller.api;

import com.xq.tmall.common.ApiResponse;
import com.xq.tmall.common.ApiResult;
import com.xq.tmall.common.BusinessException;
import com.xq.tmall.entity.Address;
import com.xq.tmall.entity.User;
import com.xq.tmall.service.AddressService;
import com.xq.tmall.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户API控制器
 */
@Slf4j
@Api(tags = "用户API")
@RestController
@RequestMapping("/api/user")
@RequiredArgsConstructor
public class UserApiController {
    
    private final UserService userService;
    private final AddressService addressService;
    
    /**
     * 获取用户信息
     */
    @ApiOperation("获取用户信息")
    @GetMapping("/profile")
    public ApiResponse<User> getProfile(HttpServletRequest request) {
        try {
            Integer userId = (Integer) request.getAttribute("userId");
            if (userId == null) {
                throw new BusinessException(ApiResult.UNAUTHORIZED);
            }
            
            User user = userService.get(userId);
            if (user == null) {
                throw new BusinessException(ApiResult.USER_NOT_FOUND);
            }
            
            // 不返回密码
            user.setUser_password(null);
            
            return ApiResponse.success(user);
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            throw new BusinessException(ApiResult.SYSTEM_ERROR, "获取用户信息失败");
        }
    }
    
    /**
     * 更新用户信息
     */
    @ApiOperation("更新用户信息")
    @PutMapping("/profile")
    public ApiResponse<String> updateProfile(@Valid @RequestBody User user, HttpServletRequest request) {
        try {
            Integer userId = (Integer) request.getAttribute("userId");
            if (userId == null) {
                throw new BusinessException(ApiResult.UNAUTHORIZED);
            }
            
            // 设置用户ID，防止修改其他用户信息
            user.setUser_id(userId);
            
            // 不允许通过此接口修改密码
            user.setUser_password(null);
            
            boolean success = userService.update(user);
            if (!success) {
                throw new BusinessException(ApiResult.DATA_OPERATION_ERROR, "更新用户信息失败");
            }
            
            return ApiResponse.success("用户信息更新成功");
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新用户信息失败", e);
            throw new BusinessException(ApiResult.SYSTEM_ERROR, "更新用户信息失败");
        }
    }
    
    /**
     * 修改密码
     */
    @ApiOperation("修改密码")
    @PutMapping("/password")
    public ApiResponse<String> changePassword(
            @ApiParam("旧密码") @RequestParam String oldPassword,
            @ApiParam("新密码") @RequestParam String newPassword,
            HttpServletRequest request) {
        
        try {
            Integer userId = (Integer) request.getAttribute("userId");
            if (userId == null) {
                throw new BusinessException(ApiResult.UNAUTHORIZED);
            }
            
            // 验证旧密码
            User currentUser = userService.get(userId);
            if (currentUser == null) {
                throw new BusinessException(ApiResult.USER_NOT_FOUND);
            }
            
            if (!currentUser.getUser_password().equals(oldPassword)) {
                throw new BusinessException(ApiResult.USER_PASSWORD_ERROR, "原密码错误");
            }
            
            // 更新密码
            User updateUser = new User();
            updateUser.setUser_id(userId);
            updateUser.setUser_password(newPassword);
            
            boolean success = userService.update(updateUser);
            if (!success) {
                throw new BusinessException(ApiResult.DATA_OPERATION_ERROR, "密码修改失败");
            }
            
            return ApiResponse.success("密码修改成功");
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("修改密码失败", e);
            throw new BusinessException(ApiResult.SYSTEM_ERROR, "修改密码失败");
        }
    }
    
    /**
     * 上传头像
     */
    @ApiOperation("上传头像")
    @PostMapping("/avatar")
    public ApiResponse<Map<String, String>> uploadAvatar(
            @ApiParam("头像文件") @RequestParam("file") MultipartFile file,
            HttpServletRequest request) {
        
        try {
            Integer userId = (Integer) request.getAttribute("userId");
            if (userId == null) {
                throw new BusinessException(ApiResult.UNAUTHORIZED);
            }
            
            if (file.isEmpty()) {
                throw new BusinessException(ApiResult.PARAM_ERROR, "请选择要上传的文件");
            }
            
            // 检查文件类型
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                throw new BusinessException(ApiResult.FILE_TYPE_ERROR, "只支持图片格式");
            }
            
            // 检查文件大小（2MB）
            if (file.getSize() > 2 * 1024 * 1024) {
                throw new BusinessException(ApiResult.FILE_SIZE_ERROR, "文件大小不能超过2MB");
            }
            
            // TODO: 实现文件上传逻辑
            // 这里需要根据实际情况实现文件存储逻辑
            String avatarUrl = "/res/images/avatars/" + userId + "_" + System.currentTimeMillis() + ".jpg";
            
            // 更新用户头像
            User updateUser = new User();
            updateUser.setUser_id(userId);
            updateUser.setUser_profile_picture_src(avatarUrl);
            
            boolean success = userService.update(updateUser);
            if (!success) {
                throw new BusinessException(ApiResult.DATA_OPERATION_ERROR, "头像更新失败");
            }
            
            Map<String, String> result = new HashMap<>();
            result.put("avatarUrl", avatarUrl);
            
            return ApiResponse.success("头像上传成功", result);
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("上传头像失败", e);
            throw new BusinessException(ApiResult.FILE_UPLOAD_ERROR, "头像上传失败");
        }
    }
    
    /**
     * 获取用户地址列表
     */
    @ApiOperation("获取用户地址列表")
    @GetMapping("/addresses")
    public ApiResponse<List<Address>> getAddresses(HttpServletRequest request) {
        try {
            Integer userId = (Integer) request.getAttribute("userId");
            if (userId == null) {
                throw new BusinessException(ApiResult.UNAUTHORIZED);
            }
            
            // 获取省份列表
            List<Address> addressList = addressService.getRoot();
            
            return ApiResponse.success(addressList);
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取地址列表失败", e);
            throw new BusinessException(ApiResult.SYSTEM_ERROR, "获取地址列表失败");
        }
    }
    
    /**
     * 根据地区ID获取下级地址
     */
    @ApiOperation("根据地区ID获取下级地址")
    @GetMapping("/addresses/{areaId}")
    public ApiResponse<List<Address>> getAddressByAreaId(@PathVariable String areaId) {
        try {
            List<Address> addressList = addressService.getList(null, areaId);
            return ApiResponse.success(addressList);
            
        } catch (Exception e) {
            log.error("获取地址失败, areaId: {}", areaId, e);
            throw new BusinessException(ApiResult.SYSTEM_ERROR, "获取地址失败");
        }
    }
    
    /**
     * 获取用户统计信息
     */
    @ApiOperation("获取用户统计信息")
    @GetMapping("/statistics")
    public ApiResponse<Map<String, Object>> getUserStatistics(HttpServletRequest request) {
        try {
            Integer userId = (Integer) request.getAttribute("userId");
            if (userId == null) {
                throw new BusinessException(ApiResult.UNAUTHORIZED);
            }
            
            // TODO: 实现用户统计信息获取
            // 这里可以统计用户的订单数量、评价数量等信息
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("orderCount", 0);
            statistics.put("reviewCount", 0);
            statistics.put("favoriteCount", 0);
            
            return ApiResponse.success(statistics);
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取用户统计信息失败", e);
            throw new BusinessException(ApiResult.SYSTEM_ERROR, "获取用户统计信息失败");
        }
    }
}
