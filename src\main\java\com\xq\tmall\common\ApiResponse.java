package com.xq.tmall.common;

import lombok.Data;

/**
 * 统一API响应格式
 * @param <T> 响应数据类型
 */
@Data
public class ApiResponse<T> {
    
    /**
     * 响应状态码
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 时间戳
     */
    private Long timestamp;
    
    public ApiResponse() {
        this.timestamp = System.currentTimeMillis();
    }
    
    public ApiResponse(Integer code, String message) {
        this();
        this.code = code;
        this.message = message;
    }
    
    public ApiResponse(Integer code, String message, T data) {
        this(code, message);
        this.data = data;
    }
    
    /**
     * 成功响应
     */
    public static <T> ApiResponse<T> success() {
        return new ApiResponse<>(ApiResult.SUCCESS.getCode(), ApiResult.SUCCESS.getMessage());
    }
    
    /**
     * 成功响应带数据
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(ApiResult.SUCCESS.getCode(), ApiResult.SUCCESS.getMessage(), data);
    }
    
    /**
     * 成功响应带消息
     */
    public static <T> ApiResponse<T> success(String message) {
        return new ApiResponse<>(ApiResult.SUCCESS.getCode(), message);
    }
    
    /**
     * 成功响应带消息和数据
     */
    public static <T> ApiResponse<T> success(String message, T data) {
        return new ApiResponse<>(ApiResult.SUCCESS.getCode(), message, data);
    }
    
    /**
     * 失败响应
     */
    public static <T> ApiResponse<T> error() {
        return new ApiResponse<>(ApiResult.ERROR.getCode(), ApiResult.ERROR.getMessage());
    }
    
    /**
     * 失败响应带消息
     */
    public static <T> ApiResponse<T> error(String message) {
        return new ApiResponse<>(ApiResult.ERROR.getCode(), message);
    }
    
    /**
     * 失败响应带状态码和消息
     */
    public static <T> ApiResponse<T> error(Integer code, String message) {
        return new ApiResponse<>(code, message);
    }
    
    /**
     * 自定义响应
     */
    public static <T> ApiResponse<T> result(ApiResult result) {
        return new ApiResponse<>(result.getCode(), result.getMessage());
    }
    
    /**
     * 自定义响应带数据
     */
    public static <T> ApiResponse<T> result(ApiResult result, T data) {
        return new ApiResponse<>(result.getCode(), result.getMessage(), data);
    }
}
