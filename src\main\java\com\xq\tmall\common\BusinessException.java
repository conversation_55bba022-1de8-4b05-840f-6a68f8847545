package com.xq.tmall.common;

import lombok.Getter;

/**
 * 业务异常类
 */
@Getter
public class BusinessException extends RuntimeException {
    
    private final Integer code;
    private final String message;
    
    public BusinessException(String message) {
        super(message);
        this.code = ApiResult.ERROR.getCode();
        this.message = message;
    }
    
    public BusinessException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
    
    public BusinessException(ApiResult result) {
        super(result.getMessage());
        this.code = result.getCode();
        this.message = result.getMessage();
    }
    
    public BusinessException(ApiResult result, String customMessage) {
        super(customMessage);
        this.code = result.getCode();
        this.message = customMessage;
    }
}
