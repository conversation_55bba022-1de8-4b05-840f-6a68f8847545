package com.xq.tmall.controller.api;

import com.xq.tmall.common.ApiResponse;
import com.xq.tmall.common.ApiResult;
import com.xq.tmall.common.BusinessException;
import com.xq.tmall.entity.ProductOrder;
import com.xq.tmall.entity.ProductOrderItem;
import com.xq.tmall.entity.User;
import com.xq.tmall.service.ProductOrderItemService;
import com.xq.tmall.service.ProductOrderService;
import com.xq.tmall.service.UserService;
import com.xq.tmall.util.PageUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单API控制器
 */
@Slf4j
@Api(tags = "订单API")
@RestController
@RequestMapping("/api/user")
@RequiredArgsConstructor
public class OrderApiController {
    
    private final ProductOrderService productOrderService;
    private final ProductOrderItemService productOrderItemService;
    private final UserService userService;
    
    /**
     * 获取订单列表
     */
    @ApiOperation("获取订单列表")
    @GetMapping("/orders")
    public ApiResponse<Map<String, Object>> getOrderList(
            @ApiParam("订单状态") @RequestParam(required = false) Byte status,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam("每页数量") @RequestParam(defaultValue = "10") Integer size,
            HttpServletRequest request) {
        
        try {
            Integer userId = (Integer) request.getAttribute("userId");
            if (userId == null) {
                throw new BusinessException(ApiResult.UNAUTHORIZED);
            }
            
            // 构建查询条件
            User user = new User();
            user.setUser_id(userId);
            
            Byte[] statusArray = null;
            if (status != null) {
                statusArray = new Byte[]{status};
            }
            
            // 分页参数
            PageUtil pageUtil = new PageUtil((page - 1) * size, size);
            
            // 查询订单列表
            List<ProductOrder> orderList = productOrderService.getList(user, statusArray, null, pageUtil);
            
            // 为每个订单设置订单项
            for (ProductOrder order : orderList) {
                ProductOrderItem orderItem = new ProductOrderItem();
                orderItem.setOrderitem_order(order);
                List<ProductOrderItem> orderItemList = productOrderItemService.getList(orderItem, null);
                order.setProductOrderItemList(orderItemList);
            }
            
            // 查询总数
            Integer total = productOrderService.getTotal(user, statusArray);
            
            // 构建响应数据
            Map<String, Object> result = new HashMap<>();
            result.put("list", orderList);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
            result.put("totalPages", (int) Math.ceil((double) total / size));
            
            return ApiResponse.success(result);
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取订单列表失败", e);
            throw new BusinessException(ApiResult.SYSTEM_ERROR, "获取订单列表失败");
        }
    }
    
    /**
     * 获取订单详情
     */
    @ApiOperation("获取订单详情")
    @GetMapping("/orders/{orderId}")
    public ApiResponse<ProductOrder> getOrderDetails(@PathVariable Integer orderId, HttpServletRequest request) {
        try {
            Integer userId = (Integer) request.getAttribute("userId");
            if (userId == null) {
                throw new BusinessException(ApiResult.UNAUTHORIZED);
            }
            
            ProductOrder order = productOrderService.get(orderId);
            if (order == null) {
                throw new BusinessException(ApiResult.ORDER_NOT_FOUND);
            }
            
            // 验证订单是否属于当前用户
            if (!order.getProductorder_user().getUser_id().equals(userId)) {
                throw new BusinessException(ApiResult.PERMISSION_DENIED, "无权访问该订单");
            }
            
            // 设置订单项
            ProductOrderItem orderItem = new ProductOrderItem();
            orderItem.setOrderitem_order(order);
            List<ProductOrderItem> orderItemList = productOrderItemService.getList(orderItem, null);
            order.setProductOrderItemList(orderItemList);
            
            return ApiResponse.success(order);
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取订单详情失败, orderId: {}", orderId, e);
            throw new BusinessException(ApiResult.SYSTEM_ERROR, "获取订单详情失败");
        }
    }
    
    /**
     * 创建订单
     */
    @ApiOperation("创建订单")
    @PostMapping("/orders")
    public ApiResponse<Map<String, Object>> createOrder(@Valid @RequestBody ProductOrder order, HttpServletRequest request) {
        try {
            Integer userId = (Integer) request.getAttribute("userId");
            if (userId == null) {
                throw new BusinessException(ApiResult.UNAUTHORIZED);
            }
            
            // 设置订单用户
            User user = new User();
            user.setUser_id(userId);
            order.setProductorder_user(user);
            
            // 设置订单状态为待支付
            order.setProductorder_status((byte) 0);
            
            // 创建订单
            boolean success = productOrderService.add(order);
            if (!success) {
                throw new BusinessException(ApiResult.DATA_OPERATION_ERROR, "创建订单失败");
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("orderId", order.getProductorder_id());
            result.put("orderCode", order.getProductorder_code());
            
            return ApiResponse.success("订单创建成功", result);
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("创建订单失败", e);
            throw new BusinessException(ApiResult.SYSTEM_ERROR, "创建订单失败");
        }
    }
    
    /**
     * 取消订单
     */
    @ApiOperation("取消订单")
    @PutMapping("/orders/{orderId}/cancel")
    public ApiResponse<String> cancelOrder(@PathVariable Integer orderId, HttpServletRequest request) {
        try {
            Integer userId = (Integer) request.getAttribute("userId");
            if (userId == null) {
                throw new BusinessException(ApiResult.UNAUTHORIZED);
            }
            
            ProductOrder order = productOrderService.get(orderId);
            if (order == null) {
                throw new BusinessException(ApiResult.ORDER_NOT_FOUND);
            }
            
            // 验证订单是否属于当前用户
            if (!order.getProductorder_user().getUser_id().equals(userId)) {
                throw new BusinessException(ApiResult.PERMISSION_DENIED, "无权操作该订单");
            }
            
            // 检查订单状态是否可以取消
            if (order.getProductorder_status() != 0 && order.getProductorder_status() != 1) {
                throw new BusinessException(ApiResult.ORDER_CANNOT_CANCEL, "订单当前状态无法取消");
            }
            
            // 更新订单状态为已取消
            order.setProductorder_status((byte) 5);
            boolean success = productOrderService.update(order);
            if (!success) {
                throw new BusinessException(ApiResult.DATA_OPERATION_ERROR, "取消订单失败");
            }
            
            return ApiResponse.success("订单取消成功");
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("取消订单失败, orderId: {}", orderId, e);
            throw new BusinessException(ApiResult.SYSTEM_ERROR, "取消订单失败");
        }
    }
    
    /**
     * 确认收货
     */
    @ApiOperation("确认收货")
    @PutMapping("/orders/{orderId}/confirm")
    public ApiResponse<String> confirmOrder(@PathVariable Integer orderId, HttpServletRequest request) {
        try {
            Integer userId = (Integer) request.getAttribute("userId");
            if (userId == null) {
                throw new BusinessException(ApiResult.UNAUTHORIZED);
            }
            
            ProductOrder order = productOrderService.get(orderId);
            if (order == null) {
                throw new BusinessException(ApiResult.ORDER_NOT_FOUND);
            }
            
            // 验证订单是否属于当前用户
            if (!order.getProductorder_user().getUser_id().equals(userId)) {
                throw new BusinessException(ApiResult.PERMISSION_DENIED, "无权操作该订单");
            }
            
            // 检查订单状态是否可以确认收货
            if (order.getProductorder_status() != 2) {
                throw new BusinessException(ApiResult.ORDER_STATUS_ERROR, "订单当前状态无法确认收货");
            }
            
            // 更新订单状态为已完成
            order.setProductorder_status((byte) 3);
            boolean success = productOrderService.update(order);
            if (!success) {
                throw new BusinessException(ApiResult.DATA_OPERATION_ERROR, "确认收货失败");
            }
            
            return ApiResponse.success("确认收货成功");
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("确认收货失败, orderId: {}", orderId, e);
            throw new BusinessException(ApiResult.SYSTEM_ERROR, "确认收货失败");
        }
    }
    
    /**
     * 支付订单
     */
    @ApiOperation("支付订单")
    @PostMapping("/orders/{orderId}/pay")
    public ApiResponse<Map<String, Object>> payOrder(
            @PathVariable Integer orderId,
            @ApiParam("支付方式") @RequestParam String paymentMethod,
            HttpServletRequest request) {
        
        try {
            Integer userId = (Integer) request.getAttribute("userId");
            if (userId == null) {
                throw new BusinessException(ApiResult.UNAUTHORIZED);
            }
            
            ProductOrder order = productOrderService.get(orderId);
            if (order == null) {
                throw new BusinessException(ApiResult.ORDER_NOT_FOUND);
            }
            
            // 验证订单是否属于当前用户
            if (!order.getProductorder_user().getUser_id().equals(userId)) {
                throw new BusinessException(ApiResult.PERMISSION_DENIED, "无权操作该订单");
            }
            
            // 检查订单状态是否可以支付
            if (order.getProductorder_status() != 0) {
                throw new BusinessException(ApiResult.ORDER_CANNOT_PAY, "订单当前状态无法支付");
            }
            
            // TODO: 实现具体的支付逻辑
            // 这里应该调用支付接口，生成支付订单等
            
            // 更新订单状态为已支付
            order.setProductorder_status((byte) 1);
            boolean success = productOrderService.update(order);
            if (!success) {
                throw new BusinessException(ApiResult.DATA_OPERATION_ERROR, "支付失败");
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("orderId", orderId);
            result.put("paymentMethod", paymentMethod);
            result.put("paymentStatus", "success");
            
            return ApiResponse.success("支付成功", result);
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("支付订单失败, orderId: {}", orderId, e);
            throw new BusinessException(ApiResult.SYSTEM_ERROR, "支付失败");
        }
    }
}
