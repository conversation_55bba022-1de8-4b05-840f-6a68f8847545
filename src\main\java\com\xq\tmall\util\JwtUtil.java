package com.xq.tmall.util;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT工具类
 */
@Slf4j
@Component
public class JwtUtil {
    
    // JWT密钥
    private static final String SECRET_KEY = "tmall-jwt-secret-key-for-authentication-2024";
    
    // Token过期时间（7天）
    private static final long EXPIRATION_TIME = 7 * 24 * 60 * 60 * 1000L;
    
    // 刷新Token过期时间（30天）
    private static final long REFRESH_EXPIRATION_TIME = 30 * 24 * 60 * 60 * 1000L;
    
    private static final SecretKey key = Keys.hmacShaKeyFor(SECRET_KEY.getBytes());
    
    /**
     * 生成用户Token
     */
    public String generateUserToken(Integer userId, String username) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        claims.put("username", username);
        claims.put("type", "user");
        return generateToken(claims, EXPIRATION_TIME);
    }
    
    /**
     * 生成管理员Token
     */
    public String generateAdminToken(Integer adminId, String adminName) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("adminId", adminId);
        claims.put("adminName", adminName);
        claims.put("type", "admin");
        return generateToken(claims, EXPIRATION_TIME);
    }
    
    /**
     * 生成刷新Token
     */
    public String generateRefreshToken(Integer userId, String type) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        claims.put("type", type);
        claims.put("refresh", true);
        return generateToken(claims, REFRESH_EXPIRATION_TIME);
    }
    
    /**
     * 生成Token
     */
    private String generateToken(Map<String, Object> claims, long expiration) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration);
        
        return Jwts.builder()
                .setClaims(claims)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(key, SignatureAlgorithm.HS256)
                .compact();
    }
    
    /**
     * 解析Token
     */
    public Claims parseToken(String token) {
        try {
            return Jwts.parserBuilder()
                    .setSigningKey(key)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
        } catch (ExpiredJwtException e) {
            log.warn("Token已过期: {}", e.getMessage());
            throw new RuntimeException("Token已过期");
        } catch (UnsupportedJwtException e) {
            log.warn("不支持的Token: {}", e.getMessage());
            throw new RuntimeException("不支持的Token");
        } catch (MalformedJwtException e) {
            log.warn("Token格式错误: {}", e.getMessage());
            throw new RuntimeException("Token格式错误");
        } catch (SecurityException e) {
            log.warn("Token签名验证失败: {}", e.getMessage());
            throw new RuntimeException("Token签名验证失败");
        } catch (IllegalArgumentException e) {
            log.warn("Token为空: {}", e.getMessage());
            throw new RuntimeException("Token为空");
        }
    }
    
    /**
     * 验证Token是否有效
     */
    public boolean validateToken(String token) {
        try {
            parseToken(token);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 从Token中获取用户ID
     */
    public Integer getUserIdFromToken(String token) {
        Claims claims = parseToken(token);
        return claims.get("userId", Integer.class);
    }
    
    /**
     * 从Token中获取管理员ID
     */
    public Integer getAdminIdFromToken(String token) {
        Claims claims = parseToken(token);
        return claims.get("adminId", Integer.class);
    }
    
    /**
     * 从Token中获取用户类型
     */
    public String getTypeFromToken(String token) {
        Claims claims = parseToken(token);
        return claims.get("type", String.class);
    }
    
    /**
     * 检查Token是否过期
     */
    public boolean isTokenExpired(String token) {
        try {
            Claims claims = parseToken(token);
            return claims.getExpiration().before(new Date());
        } catch (Exception e) {
            return true;
        }
    }
    
    /**
     * 刷新Token
     */
    public String refreshToken(String refreshToken) {
        Claims claims = parseToken(refreshToken);
        Boolean isRefresh = claims.get("refresh", Boolean.class);
        if (isRefresh == null || !isRefresh) {
            throw new RuntimeException("无效的刷新Token");
        }
        
        Integer userId = claims.get("userId", Integer.class);
        String type = claims.get("type", String.class);
        
        if ("user".equals(type)) {
            return generateUserToken(userId, null);
        } else if ("admin".equals(type)) {
            return generateAdminToken(userId, null);
        } else {
            throw new RuntimeException("未知的用户类型");
        }
    }
}
