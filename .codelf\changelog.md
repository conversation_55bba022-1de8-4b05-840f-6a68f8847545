# 项目变更日志

## [2024-12-19] 重大重构 - 前后端分离架构升级

### 新增功能

#### 🎯 统一API响应格式
- 新增 `ApiResponse<T>` 通用响应包装类
- 新增 `ApiResult` 响应状态码枚举
- 新增 `BusinessException` 业务异常类
- 新增 `GlobalExceptionHandler` 全局异常处理器

#### 🔐 JWT认证体系
- 新增 `JwtUtil` JWT工具类，支持Token生成、解析、验证
- 新增 `JwtInterceptor` JWT拦截器，自动验证Token和权限
- 新增 `WebConfig` Web配置类，注册拦截器和CORS配置
- 添加JWT依赖：jjwt-api, jjwt-impl, jjwt-jackson

#### 🚀 API控制器重构
- 新增 `AuthApiController` 认证API控制器
  - 用户登录/注册
  - 管理员登录
  - 验证码获取
  - Token刷新
- 新增 `ProductApiController` 商品API控制器
  - 商品列表/详情/搜索
  - 分类管理
- 新增 `UserApiController` 用户API控制器
  - 用户信息管理
  - 密码修改
  - 头像上传
- 新增 `OrderApiController` 订单API控制器
  - 订单CRUD操作
  - 订单状态管理
  - 支付处理
- 新增 `AdminApiController` 管理员API控制器
  - 后台管理功能
  - 统计信息

#### 📄 页面控制器分离
- 新增 `PageController` 专门处理页面跳转
- 分离页面跳转逻辑和业务逻辑

#### 🎨 前端工具重构
- 新增 `api-client.js` 统一API客户端工具
  - 自动Token管理
  - 请求/响应拦截
  - 错误处理
  - Token自动刷新
- 新增 `auth-manager.js` 认证管理器
  - 登录/注册处理
  - 权限验证
  - 用户状态管理
  - UI状态更新
- 新增 `fore_login_new.js` 重构后的登录页面JavaScript

#### 📋 数据传输对象
- 新增 `LoginRequest` 登录请求DTO
- 新增 `LoginResponse` 登录响应DTO
- 新增 `RegisterRequest` 注册请求DTO

### 修改功能

#### 🔧 Service层增强
- 更新 `UserService` 接口，新增 `isExist` 方法
- 更新 `AdminService` 接口，新增 `login` 方法，重命名原 `login` 为 `loginCheck`
- 更新对应的实现类和Mapper

#### 🗄️ 数据访问层扩展
- 更新 `UserMapper` 新增 `selectByUsernameOrEmail` 方法
- 更新 `AdminMapper` 新增 `selectByNameAndPassword` 方法
- 更新对应的XML映射文件

#### 🎯 BaseController增强
- 新增JWT方式的权限检查方法
- 保持向后兼容，支持Session和JWT两种认证方式

#### 📱 前端页面更新
- 更新 `loginPage.jsp` 引入新的API工具
- 示例展示如何从JSTL渲染改为JavaScript处理JSON

### 技术改进

#### 🏗️ 架构升级
- 从传统MVC架构升级为前后端分离架构
- 实现RESTful API设计规范
- 统一错误处理和响应格式

#### 🔒 安全增强
- 使用JWT替代Session认证
- 支持Token自动刷新机制
- 实现基于角色的权限控制

#### 🚀 性能优化
- 减少服务器渲染压力
- 支持分布式部署
- 无需Session共享

#### 📚 开发体验
- 统一的API调用方式
- 完善的错误处理机制
- 自动化的权限验证

### 兼容性说明

- ✅ 完全兼容现有数据库结构
- ✅ 保留原有Session认证方式
- ✅ 支持新旧系统并存
- ✅ 渐进式迁移方案

### 文档更新

- 新增 `项目重构文档.md` 详细重构说明
- 更新 `issues/tmall-api-refactor.md` 任务跟踪
- 新增 `.codelf/project.md` 项目文档
- 新增 `.codelf/changelog.md` 变更日志

### 下一步计划

1. 继续重构更多JSP页面
2. 完善API接口测试
3. 优化前端用户体验
4. 添加更多业务功能API
5. 性能测试和优化

### 影响范围

- 🎯 **开发效率**: 前后端可并行开发
- 🔧 **维护性**: 职责分离，代码更清晰  
- 📈 **扩展性**: 支持移动端、小程序等多端接入
- ⚡ **性能**: 减少服务器渲染压力
- 👥 **用户体验**: 页面响应更快，交互更流畅
