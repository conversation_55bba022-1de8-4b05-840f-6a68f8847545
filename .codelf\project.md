# Tmall Demo 项目文档

## 项目概述

这是一个基于Spring Boot的电商系统，已完成从传统JSP+JSTL架构到现代化前后端分离架构的重构。

### 技术栈

**后端技术**:
- Spring Boot 2.1.6
- MyBatis Plus 2.3.1
- MySQL 数据库
- JWT 认证
- Swagger2 API文档
- Redis 缓存
- 支付宝/微信支付

**前端技术**:
- JSP + JavaScript
- jQuery
- Bootstrap
- 统一API客户端
- JWT Token管理

### 项目结构

```
src/main/java/com/xq/tmall/
├── common/              # 通用组件
│   ├── ApiResponse.java     # 统一API响应格式
│   ├── ApiResult.java       # 响应状态枚举
│   ├── BusinessException.java # 业务异常
│   └── GlobalExceptionHandler.java # 全局异常处理
├── config/              # 配置类
│   ├── JwtInterceptor.java  # JWT拦截器
│   └── WebConfig.java       # Web配置
├── controller/          # 控制器
│   ├── PageController.java  # 页面跳转控制器
│   ├── BaseController.java  # 基础控制器
│   ├── admin/              # 管理员控制器(传统)
│   ├── fore/               # 前台控制器(传统)
│   └── api/                # API控制器(新增)
│       ├── AuthApiController.java    # 认证API
│       ├── ProductApiController.java # 商品API
│       ├── UserApiController.java    # 用户API
│       ├── OrderApiController.java   # 订单API
│       └── AdminApiController.java   # 管理员API
├── dao/                 # 数据访问层
├── dto/                 # 数据传输对象
│   ├── LoginRequest.java    # 登录请求
│   ├── LoginResponse.java   # 登录响应
│   └── RegisterRequest.java # 注册请求
├── entity/              # 实体类
├── service/             # 服务层
├── util/                # 工具类
│   └── JwtUtil.java         # JWT工具类
└── pay/                 # 支付相关
```

### 核心功能模块

1. **用户管理**: 用户注册、登录、个人信息管理
2. **商品管理**: 商品展示、分类管理、搜索功能
3. **订单管理**: 购物车、下单、支付、订单跟踪
4. **后台管理**: 商品管理、用户管理、订单管理、统计分析
5. **支付系统**: 支持支付宝、微信支付

### API接口设计

#### 认证相关
- POST /api/auth/user/login - 用户登录
- POST /api/auth/admin/login - 管理员登录
- POST /api/auth/register - 用户注册
- GET /api/auth/captcha - 获取验证码
- POST /api/auth/refresh - 刷新Token
- POST /api/auth/logout - 退出登录

#### 商品相关
- GET /api/public/products - 获取商品列表
- GET /api/public/products/{id} - 获取商品详情
- GET /api/public/products/search - 搜索商品
- GET /api/public/categories - 获取分类列表

#### 用户相关
- GET /api/user/profile - 获取用户信息
- PUT /api/user/profile - 更新用户信息
- PUT /api/user/password - 修改密码
- POST /api/user/avatar - 上传头像

#### 订单相关
- GET /api/user/orders - 获取订单列表
- POST /api/user/orders - 创建订单
- PUT /api/user/orders/{id}/cancel - 取消订单
- POST /api/user/orders/{id}/pay - 支付订单

### 数据库设计

主要数据表：
- user: 用户表
- admin: 管理员表
- category: 分类表
- product: 商品表
- product_image: 商品图片表
- product_order: 订单表
- product_order_item: 订单项表
- review: 评价表
- address: 地址表

### 安全机制

1. **JWT认证**: 使用JWT Token进行用户认证
2. **权限控制**: 基于角色的访问控制(用户/管理员)
3. **参数验证**: 使用Bean Validation进行参数校验
4. **异常处理**: 全局异常处理机制
5. **CORS配置**: 支持跨域请求

### 部署说明

1. **环境要求**: Java 8+, MySQL 5.7+, Maven 3.6+
2. **配置文件**: application.yml中配置数据库连接
3. **数据库初始化**: 执行sql/db.sql初始化数据库
4. **启动命令**: mvn spring-boot:run

### 开发规范

1. **代码风格**: 遵循阿里巴巴Java开发手册
2. **API设计**: RESTful风格，统一响应格式
3. **异常处理**: 使用BusinessException处理业务异常
4. **日志记录**: 使用Log4j2记录关键操作日志
5. **注释规范**: 重要方法必须添加Javadoc注释
