package com.xq.tmall.controller.api;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.CircleCaptcha;
import com.baomidou.mybatisplus.toolkit.IdWorker;
import com.xq.tmall.common.ApiResponse;
import com.xq.tmall.common.ApiResult;
import com.xq.tmall.common.BusinessException;
import com.xq.tmall.dto.LoginRequest;
import com.xq.tmall.dto.LoginResponse;
import com.xq.tmall.dto.RegisterRequest;
import com.xq.tmall.entity.Admin;
import com.xq.tmall.entity.ApiVerCodeResp;
import com.xq.tmall.entity.User;
import com.xq.tmall.service.AdminService;
import com.xq.tmall.service.UserService;
import com.xq.tmall.util.JwtUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 认证API控制器
 */
@Slf4j
@Api(tags = "认证API")
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
public class AuthApiController {
    
    private final UserService userService;
    private final AdminService adminService;
    private final JwtUtil jwtUtil;
    
    /**
     * 用户登录
     */
    @ApiOperation("用户登录")
    @PostMapping("/user/login")
    public ApiResponse<LoginResponse> userLogin(@Valid @RequestBody LoginRequest request) {
        // 验证用户名和密码
        User user = userService.login(request.getUsername(), request.getPassword());
        if (user == null) {
            throw new BusinessException(ApiResult.USER_PASSWORD_ERROR);
        }
        
        // 生成Token
        String accessToken = jwtUtil.generateUserToken(user.getUser_id(), user.getUser_name());
        String refreshToken = jwtUtil.generateRefreshToken(user.getUser_id(), "user");
        
        // 构建响应
        LoginResponse response = new LoginResponse();
        response.setAccessToken(accessToken);
        response.setRefreshToken(refreshToken);
        response.setExpiresIn(7 * 24 * 60 * 60L); // 7天
        
        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo();
        userInfo.setId(user.getUser_id());
        userInfo.setUsername(user.getUser_name());
        userInfo.setNickname(user.getUser_nickname());
        userInfo.setAvatar(user.getUser_profile_picture_src());
        userInfo.setType("user");
        response.setUserInfo(userInfo);
        
        return ApiResponse.success("登录成功", response);
    }
    
    /**
     * 管理员登录
     */
    @ApiOperation("管理员登录")
    @PostMapping("/admin/login")
    public ApiResponse<LoginResponse> adminLogin(@Valid @RequestBody LoginRequest request) {
        // 验证管理员用户名和密码
        Admin admin = adminService.login(request.getUsername(), request.getPassword());
        if (admin == null) {
            throw new BusinessException(ApiResult.ADMIN_PASSWORD_ERROR);
        }
        
        // 生成Token
        String accessToken = jwtUtil.generateAdminToken(admin.getAdmin_id(), admin.getAdmin_name());
        String refreshToken = jwtUtil.generateRefreshToken(admin.getAdmin_id(), "admin");
        
        // 构建响应
        LoginResponse response = new LoginResponse();
        response.setAccessToken(accessToken);
        response.setRefreshToken(refreshToken);
        response.setExpiresIn(7 * 24 * 60 * 60L); // 7天
        
        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo();
        userInfo.setId(admin.getAdmin_id());
        userInfo.setUsername(admin.getAdmin_name());
        userInfo.setNickname(admin.getAdmin_nickname());
        userInfo.setAvatar(admin.getAdmin_profile_picture_src());
        userInfo.setType("admin");
        response.setUserInfo(userInfo);
        
        return ApiResponse.success("登录成功", response);
    }
    
    /**
     * 用户注册
     */
    @ApiOperation("用户注册")
    @PostMapping("/register")
    public ApiResponse<String> register(@Valid @RequestBody RegisterRequest request) {
        // 验证密码确认
        if (!request.getPassword().equals(request.getConfirmPassword())) {
            throw new BusinessException(ApiResult.PARAM_ERROR, "两次输入的密码不一致");
        }
        
        // 检查用户名是否已存在
        if (userService.isExist(request.getUsername(), null)) {
            throw new BusinessException(ApiResult.USER_ALREADY_EXISTS);
        }
        
        // 创建用户
        User user = new User();
        user.setUser_name(request.getUsername());
        user.setUser_password(request.getPassword());
        user.setUser_nickname(request.getNickname());
        // 设置其他字段...
        
        boolean success = userService.add(user);
        if (!success) {
            throw new BusinessException(ApiResult.DATA_OPERATION_ERROR, "注册失败");
        }
        
        return ApiResponse.success("注册成功");
    }
    
    /**
     * 获取验证码
     */
    @ApiOperation("获取验证码")
    @GetMapping("/captcha")
    public ApiResponse<ApiVerCodeResp> getCaptcha() {
        CircleCaptcha captcha = CaptchaUtil.createCircleCaptcha(140, 38, 4, 20);
        ApiVerCodeResp response = new ApiVerCodeResp(
            String.valueOf(IdWorker.getId()), 
            captcha.getImageBase64Data(), 
            captcha.getCode().toLowerCase()
        );
        return ApiResponse.success(response);
    }
    
    /**
     * 刷新Token
     */
    @ApiOperation("刷新Token")
    @PostMapping("/refresh")
    public ApiResponse<LoginResponse> refreshToken(@RequestParam String refreshToken) {
        try {
            String newAccessToken = jwtUtil.refreshToken(refreshToken);
            
            LoginResponse response = new LoginResponse();
            response.setAccessToken(newAccessToken);
            response.setRefreshToken(refreshToken);
            response.setExpiresIn(7 * 24 * 60 * 60L);
            
            return ApiResponse.success("Token刷新成功", response);
        } catch (Exception e) {
            throw new BusinessException(ApiResult.TOKEN_INVALID, "刷新Token无效");
        }
    }
    
    /**
     * 退出登录
     */
    @ApiOperation("退出登录")
    @PostMapping("/logout")
    public ApiResponse<String> logout(HttpServletRequest request) {
        // JWT是无状态的，客户端删除Token即可
        // 这里可以实现Token黑名单机制（可选）
        return ApiResponse.success("退出成功");
    }
}
