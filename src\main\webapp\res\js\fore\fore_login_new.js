/**
 * 前台登录页面JavaScript (重构版本)
 * 使用API调用替代传统表单提交
 */

$(document).ready(function() {
    // 初始化页面
    initLoginPage();
    
    // 绑定事件
    bindEvents();
    
    // 获取验证码
    loadCaptcha();
});

/**
 * 初始化登录页面
 */
function initLoginPage() {
    // 检查是否已登录
    if (authManager.isLoggedIn()) {
        // 根据用户类型跳转
        if (authManager.isAdmin()) {
            window.location.href = '/admin/home';
        } else {
            window.location.href = '/';
        }
        return;
    }
    
    // 初始化登录切换
    initLoginSwitch();
}

/**
 * 初始化登录方式切换
 */
function initLoginSwitch() {
    // 默认显示密码登录
    showPasswordLogin();
    
    // 切换到二维码登录
    $('#loginSwitch').click(function() {
        if ($('.pwdLogin').is(':visible')) {
            showQrcodeLogin();
        } else {
            showPasswordLogin();
        }
    });
    
    // 密码登录链接
    $('#pwdLogin').click(function() {
        showPasswordLogin();
    });
}

/**
 * 显示密码登录
 */
function showPasswordLogin() {
    $('.pwdLogin').show();
    $('.qrcodeLogin').hide();
    $('#loginSwitch').text('扫码登录');
}

/**
 * 显示二维码登录
 */
function showQrcodeLogin() {
    $('.pwdLogin').hide();
    $('.qrcodeLogin').show();
    $('#loginSwitch').text('密码登录');
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 登录表单提交
    $('.loginForm').on('submit', function(e) {
        e.preventDefault();
        handleLogin();
    });
    
    // 验证码点击刷新
    $('#img_code').click(function() {
        loadCaptcha();
    });
    
    // 输入框焦点事件
    $('.loginInput, .loginCode').focus(function() {
        hideErrorMessage();
    });
    
    // 回车键登录
    $('.loginInput, .loginCode').keypress(function(e) {
        if (e.which === 13) {
            handleLogin();
        }
    });
}

/**
 * 处理登录
 */
async function handleLogin() {
    // 获取表单数据
    const username = $('#name').val().trim();
    const password = $('#password').val().trim();
    const captcha = $('#code').val().trim();
    
    // 验证输入
    if (!username) {
        showErrorMessage('请输入用户名');
        $('#name').focus();
        return;
    }
    
    if (!password) {
        showErrorMessage('请输入密码');
        $('#password').focus();
        return;
    }
    
    if (!captcha) {
        showErrorMessage('请输入验证码');
        $('#code').focus();
        return;
    }
    
    // 显示加载状态
    showLoading();
    
    try {
        // 调用登录API
        const result = await authManager.login(username, password, 'user', captcha, currentCaptchaId);
        
        if (result.success) {
            showSuccessMessage('登录成功，正在跳转...');
            
            // 延迟跳转，让用户看到成功提示
            setTimeout(() => {
                window.location.href = '/';
            }, 1000);
        } else {
            showErrorMessage(result.message || '登录失败');
            // 刷新验证码
            loadCaptcha();
        }
    } catch (error) {
        console.error('登录失败:', error);
        showErrorMessage(error.message || '登录失败，请稍后重试');
        // 刷新验证码
        loadCaptcha();
    } finally {
        hideLoading();
    }
}

/**
 * 加载验证码
 */
let currentCaptchaId = null;

async function loadCaptcha() {
    try {
        const response = await authAPI.getCaptcha();
        if (response.code === 200) {
            const captchaData = response.data;
            currentCaptchaId = captchaData.id;
            $('#img_code').attr('src', 'data:image/png;base64,' + captchaData.image);
            $('#code').val(''); // 清空验证码输入框
        } else {
            console.error('获取验证码失败:', response.message);
        }
    } catch (error) {
        console.error('获取验证码失败:', error);
    }
}

/**
 * 显示错误消息
 */
function showErrorMessage(message) {
    $('#error_message_p').text(message);
    $('.error_message').show();
}

/**
 * 隐藏错误消息
 */
function hideErrorMessage() {
    $('.error_message').hide();
    $('#error_message_p').text('');
}

/**
 * 显示成功消息
 */
function showSuccessMessage(message) {
    $('#error_message_p').text(message);
    $('.error_message').removeClass('error').addClass('success').show();
}

/**
 * 显示加载状态
 */
function showLoading() {
    $('.loginButton').prop('disabled', true).val('登录中...');
}

/**
 * 隐藏加载状态
 */
function hideLoading() {
    $('.loginButton').prop('disabled', false).val('登 录');
}

/**
 * 管理员登录处理
 */
async function handleAdminLogin() {
    // 获取表单数据
    const username = $('#name').val().trim();
    const password = $('#password').val().trim();
    const captcha = $('#code').val().trim();
    
    // 验证输入
    if (!username || !password || !captcha) {
        showErrorMessage('请填写完整的登录信息');
        return;
    }
    
    // 显示加载状态
    showLoading();
    
    try {
        // 调用管理员登录API
        const result = await authManager.login(username, password, 'admin', captcha, currentCaptchaId);
        
        if (result.success) {
            showSuccessMessage('登录成功，正在跳转...');
            
            // 延迟跳转到管理员首页
            setTimeout(() => {
                window.location.href = '/admin/home';
            }, 1000);
        } else {
            showErrorMessage(result.message || '登录失败');
            loadCaptcha();
        }
    } catch (error) {
        console.error('管理员登录失败:', error);
        showErrorMessage(error.message || '登录失败，请稍后重试');
        loadCaptcha();
    } finally {
        hideLoading();
    }
}

// 如果是管理员登录页面，使用管理员登录处理
if (window.location.pathname.includes('/admin')) {
    $(document).ready(function() {
        $('.loginForm').off('submit').on('submit', function(e) {
            e.preventDefault();
            handleAdminLogin();
        });
    });
}

// 添加CSS样式
$(document).ready(function() {
    // 添加成功消息样式
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .error_message.success {
                color: #52c41a;
                border-color: #52c41a;
                background-color: #f6ffed;
            }
            .error_message.success p {
                color: #52c41a;
            }
            .loginButton:disabled {
                background-color: #ccc;
                cursor: not-allowed;
            }
        `)
        .appendTo('head');
});
