package com.xq.tmall.config;

import com.alibaba.fastjson.JSON;
import com.xq.tmall.common.ApiResponse;
import com.xq.tmall.common.ApiResult;
import com.xq.tmall.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * JWT拦截器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtInterceptor implements HandlerInterceptor {
    
    private final JwtUtil jwtUtil;
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 预检请求直接放行
        if ("OPTIONS".equals(request.getMethod())) {
            return true;
        }
        
        // 获取请求路径
        String requestURI = request.getRequestURI();
        
        // 白名单路径，不需要Token验证
        if (isWhiteList(requestURI)) {
            return true;
        }
        
        // 获取Token
        String token = getTokenFromRequest(request);
        
        if (!StringUtils.hasText(token)) {
            writeErrorResponse(response, ApiResult.LOGIN_REQUIRED);
            return false;
        }
        
        try {
            // 验证Token
            if (!jwtUtil.validateToken(token)) {
                writeErrorResponse(response, ApiResult.TOKEN_INVALID);
                return false;
            }
            
            // 检查Token类型和权限
            String type = jwtUtil.getTypeFromToken(token);
            if (!checkPermission(requestURI, type)) {
                writeErrorResponse(response, ApiResult.PERMISSION_DENIED);
                return false;
            }
            
            // 将用户信息存储到请求属性中
            if ("user".equals(type)) {
                Integer userId = jwtUtil.getUserIdFromToken(token);
                request.setAttribute("userId", userId);
                request.setAttribute("userType", "user");
            } else if ("admin".equals(type)) {
                Integer adminId = jwtUtil.getAdminIdFromToken(token);
                request.setAttribute("adminId", adminId);
                request.setAttribute("userType", "admin");
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("Token验证失败: {}", e.getMessage());
            writeErrorResponse(response, ApiResult.TOKEN_INVALID);
            return false;
        }
    }
    
    /**
     * 从请求中获取Token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        // 从Header中获取
        String bearerToken = request.getHeader("Authorization");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        
        // 从参数中获取
        return request.getParameter("token");
    }
    
    /**
     * 检查是否为白名单路径
     */
    private boolean isWhiteList(String requestURI) {
        // 静态资源
        if (requestURI.startsWith("/res/") || 
            requestURI.startsWith("/static/") ||
            requestURI.endsWith(".css") ||
            requestURI.endsWith(".js") ||
            requestURI.endsWith(".png") ||
            requestURI.endsWith(".jpg") ||
            requestURI.endsWith(".jpeg") ||
            requestURI.endsWith(".gif") ||
            requestURI.endsWith(".ico")) {
            return true;
        }
        
        // 页面跳转（非API接口）
        if (!requestURI.startsWith("/api/")) {
            return true;
        }
        
        // API白名单
        String[] whiteList = {
            "/api/auth/login",
            "/api/auth/register", 
            "/api/auth/captcha",
            "/api/auth/refresh",
            "/api/public/"
        };
        
        for (String path : whiteList) {
            if (requestURI.startsWith(path)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查权限
     */
    private boolean checkPermission(String requestURI, String userType) {
        // 管理员接口只允许管理员访问
        if (requestURI.startsWith("/api/admin/")) {
            return "admin".equals(userType);
        }
        
        // 用户接口只允许用户访问
        if (requestURI.startsWith("/api/user/")) {
            return "user".equals(userType);
        }
        
        // 其他接口都可以访问
        return true;
    }
    
    /**
     * 写入错误响应
     */
    private void writeErrorResponse(HttpServletResponse response, ApiResult result) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        
        ApiResponse<String> apiResponse = ApiResponse.result(result);
        String jsonResponse = JSON.toJSONString(apiResponse);
        
        response.getWriter().write(jsonResponse);
    }
}
