package com.xq.tmall.common;

import lombok.Getter;

/**
 * API响应结果枚举
 */
@Getter
public enum ApiResult {
    
    // 通用响应
    SUCCESS(200, "操作成功"),
    ERROR(500, "操作失败"),
    
    // 参数相关
    PARAM_ERROR(400, "参数错误"),
    PARAM_MISSING(400, "缺少必要参数"),
    PARAM_INVALID(400, "参数格式不正确"),
    
    // 认证相关
    UNAUTHORIZED(401, "未授权访问"),
    TOKEN_INVALID(401, "Token无效"),
    TOKEN_EXPIRED(401, "Token已过期"),
    LOGIN_REQUIRED(401, "请先登录"),
    PERMISSION_DENIED(403, "权限不足"),
    
    // 用户相关
    USER_NOT_FOUND(404, "用户不存在"),
    USER_ALREADY_EXISTS(409, "用户已存在"),
    USER_PASSWORD_ERROR(400, "密码错误"),
    USER_DISABLED(403, "用户已被禁用"),
    
    // 管理员相关
    ADMIN_NOT_FOUND(404, "管理员不存在"),
    ADMIN_PASSWORD_ERROR(400, "管理员密码错误"),
    ADMIN_PERMISSION_DENIED(403, "管理员权限不足"),
    
    // 商品相关
    PRODUCT_NOT_FOUND(404, "商品不存在"),
    PRODUCT_OUT_OF_STOCK(400, "商品库存不足"),
    CATEGORY_NOT_FOUND(404, "分类不存在"),
    
    // 订单相关
    ORDER_NOT_FOUND(404, "订单不存在"),
    ORDER_STATUS_ERROR(400, "订单状态错误"),
    ORDER_CANNOT_CANCEL(400, "订单无法取消"),
    ORDER_CANNOT_PAY(400, "订单无法支付"),
    
    // 文件相关
    FILE_UPLOAD_ERROR(500, "文件上传失败"),
    FILE_TYPE_ERROR(400, "文件类型不支持"),
    FILE_SIZE_ERROR(400, "文件大小超出限制"),
    
    // 验证码相关
    CAPTCHA_ERROR(400, "验证码错误"),
    CAPTCHA_EXPIRED(400, "验证码已过期"),
    
    // 数据相关
    DATA_NOT_FOUND(404, "数据不存在"),
    DATA_ALREADY_EXISTS(409, "数据已存在"),
    DATA_OPERATION_ERROR(500, "数据操作失败"),
    
    // 系统相关
    SYSTEM_ERROR(500, "系统错误"),
    NETWORK_ERROR(500, "网络错误"),
    DATABASE_ERROR(500, "数据库错误");
    
    private final Integer code;
    private final String message;
    
    ApiResult(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
}
