package com.xq.tmall.controller.api;

import com.xq.tmall.common.ApiResponse;
import com.xq.tmall.common.ApiResult;
import com.xq.tmall.common.BusinessException;
import com.xq.tmall.entity.*;
import com.xq.tmall.service.*;
import com.xq.tmall.util.PageUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 管理员API控制器
 */
@Slf4j
@Api(tags = "管理员API")
@RestController
@RequestMapping("/api/admin")
@RequiredArgsConstructor
public class AdminApiController {
    
    private final CategoryService categoryService;
    private final ProductService productService;
    private final UserService userService;
    private final ProductOrderService productOrderService;
    private final ReviewService reviewService;
    private final AdminService adminService;
    
    // ==================== 分类管理 ====================
    
    /**
     * 获取分类列表
     */
    @ApiOperation("获取分类列表")
    @GetMapping("/categories")
    public ApiResponse<Map<String, Object>> getCategories(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam("每页数量") @RequestParam(defaultValue = "10") Integer size) {
        
        try {
            PageUtil pageUtil = new PageUtil((page - 1) * size, size);
            List<Category> categoryList = categoryService.getList(null, pageUtil);
            Integer total = categoryService.getTotal(null);
            
            Map<String, Object> result = new HashMap<>();
            result.put("list", categoryList);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
            result.put("totalPages", (int) Math.ceil((double) total / size));
            
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("获取分类列表失败", e);
            throw new BusinessException(ApiResult.SYSTEM_ERROR, "获取分类列表失败");
        }
    }
    
    /**
     * 添加分类
     */
    @ApiOperation("添加分类")
    @PostMapping("/categories")
    public ApiResponse<String> addCategory(@Valid @RequestBody Category category) {
        try {
            boolean success = categoryService.add(category);
            if (!success) {
                throw new BusinessException(ApiResult.DATA_OPERATION_ERROR, "添加分类失败");
            }
            
            return ApiResponse.success("分类添加成功");
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("添加分类失败", e);
            throw new BusinessException(ApiResult.SYSTEM_ERROR, "添加分类失败");
        }
    }
    
    /**
     * 更新分类
     */
    @ApiOperation("更新分类")
    @PutMapping("/categories/{categoryId}")
    public ApiResponse<String> updateCategory(@PathVariable Integer categoryId, @Valid @RequestBody Category category) {
        try {
            category.setCategory_id(categoryId);
            boolean success = categoryService.update(category);
            if (!success) {
                throw new BusinessException(ApiResult.DATA_OPERATION_ERROR, "更新分类失败");
            }
            
            return ApiResponse.success("分类更新成功");
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新分类失败, categoryId: {}", categoryId, e);
            throw new BusinessException(ApiResult.SYSTEM_ERROR, "更新分类失败");
        }
    }
    
    /**
     * 删除分类
     */
    @ApiOperation("删除分类")
    @DeleteMapping("/categories/{categoryId}")
    public ApiResponse<String> deleteCategory(@PathVariable Integer categoryId) {
        try {
            // 检查分类下是否有商品
            Product queryProduct = new Product();
            Category category = new Category();
            category.setCategory_id(categoryId);
            queryProduct.setProduct_category(category);

            Integer productCount = productService.getTotal(queryProduct, null);
            if (productCount > 0) {
                throw new BusinessException(ApiResult.DATA_OPERATION_ERROR, "该分类下还有商品，无法删除");
            }

            boolean success = categoryService.delete(categoryId);
            if (!success) {
                throw new BusinessException(ApiResult.DATA_OPERATION_ERROR, "删除分类失败");
            }

            return ApiResponse.success("分类删除成功");

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除分类失败, categoryId: {}", categoryId, e);
            throw new BusinessException(ApiResult.SYSTEM_ERROR, "删除分类失败");
        }
    }
    
    // ==================== 商品管理 ====================
    
    /**
     * 获取商品列表
     */
    @ApiOperation("获取商品列表")
    @GetMapping("/products")
    public ApiResponse<Map<String, Object>> getProducts(
            @ApiParam("分类ID") @RequestParam(required = false) Integer categoryId,
            @ApiParam("商品名称") @RequestParam(required = false) String productName,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam("每页数量") @RequestParam(defaultValue = "10") Integer size) {
        
        try {
            Product queryProduct = new Product();
            if (categoryId != null) {
                Category category = new Category();
                category.setCategory_id(categoryId);
                queryProduct.setProduct_category(category);
            }
            if (productName != null && !productName.trim().isEmpty()) {
                queryProduct.setProduct_name(productName.trim());
            }
            
            PageUtil pageUtil = new PageUtil((page - 1) * size, size);
            List<Product> productList = productService.getList(queryProduct, null, null, pageUtil);
            Integer total = productService.getTotal(queryProduct, null);
            
            Map<String, Object> result = new HashMap<>();
            result.put("list", productList);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
            result.put("totalPages", (int) Math.ceil((double) total / size));
            
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("获取商品列表失败", e);
            throw new BusinessException(ApiResult.SYSTEM_ERROR, "获取商品列表失败");
        }
    }
    
    /**
     * 添加商品
     */
    @ApiOperation("添加商品")
    @PostMapping("/products")
    public ApiResponse<String> addProduct(@Valid @RequestBody Product product) {
        try {
            boolean success = productService.add(product);
            if (!success) {
                throw new BusinessException(ApiResult.DATA_OPERATION_ERROR, "添加商品失败");
            }
            
            return ApiResponse.success("商品添加成功");
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("添加商品失败", e);
            throw new BusinessException(ApiResult.SYSTEM_ERROR, "添加商品失败");
        }
    }
    
    /**
     * 更新商品
     */
    @ApiOperation("更新商品")
    @PutMapping("/products/{productId}")
    public ApiResponse<String> updateProduct(@PathVariable Integer productId, @Valid @RequestBody Product product) {
        try {
            product.setProduct_id(productId);
            boolean success = productService.update(product);
            if (!success) {
                throw new BusinessException(ApiResult.DATA_OPERATION_ERROR, "更新商品失败");
            }
            
            return ApiResponse.success("商品更新成功");
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新商品失败, productId: {}", productId, e);
            throw new BusinessException(ApiResult.SYSTEM_ERROR, "更新商品失败");
        }
    }
    
    /**
     * 删除商品
     */
    @ApiOperation("删除商品")
    @DeleteMapping("/products/{productId}")
    public ApiResponse<String> deleteProduct(@PathVariable Integer productId) {
        try {
            boolean success = productService.delete(productId);
            if (!success) {
                throw new BusinessException(ApiResult.DATA_OPERATION_ERROR, "删除商品失败");
            }
            
            return ApiResponse.success("商品删除成功");
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除商品失败, productId: {}", productId, e);
            throw new BusinessException(ApiResult.SYSTEM_ERROR, "删除商品失败");
        }
    }
    
    // ==================== 用户管理 ====================
    
    /**
     * 获取用户列表
     */
    @ApiOperation("获取用户列表")
    @GetMapping("/users")
    public ApiResponse<Map<String, Object>> getUsers(
            @ApiParam("用户名") @RequestParam(required = false) String username,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam("每页数量") @RequestParam(defaultValue = "10") Integer size) {
        
        try {
            User queryUser = new User();
            if (username != null && !username.trim().isEmpty()) {
                queryUser.setUser_name(username.trim());
            }
            
            PageUtil pageUtil = new PageUtil((page - 1) * size, size);
            List<User> userList = userService.getList(queryUser, null, pageUtil);
            
            // 不返回密码
            userList.forEach(user -> user.setUser_password(null));
            
            Integer total = userService.getTotal(queryUser);
            
            Map<String, Object> result = new HashMap<>();
            result.put("list", userList);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
            result.put("totalPages", (int) Math.ceil((double) total / size));
            
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("获取用户列表失败", e);
            throw new BusinessException(ApiResult.SYSTEM_ERROR, "获取用户列表失败");
        }
    }
    
    // ==================== 订单管理 ====================
    
    /**
     * 获取订单列表
     */
    @ApiOperation("获取订单列表")
    @GetMapping("/orders")
    public ApiResponse<Map<String, Object>> getOrders(
            @ApiParam("订单状态") @RequestParam(required = false) Byte status,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam("每页数量") @RequestParam(defaultValue = "10") Integer size) {
        
        try {
            Byte[] statusArray = null;
            if (status != null) {
                statusArray = new Byte[]{status};
            }
            
            PageUtil pageUtil = new PageUtil((page - 1) * size, size);
            List<ProductOrder> orderList = productOrderService.getList(null, statusArray, null, pageUtil);
            Integer total = productOrderService.getTotal(null, statusArray);
            
            Map<String, Object> result = new HashMap<>();
            result.put("list", orderList);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
            result.put("totalPages", (int) Math.ceil((double) total / size));
            
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("获取订单列表失败", e);
            throw new BusinessException(ApiResult.SYSTEM_ERROR, "获取订单列表失败");
        }
    }
    
    // ==================== 统计信息 ====================
    
    /**
     * 获取统计信息
     */
    @ApiOperation("获取统计信息")
    @GetMapping("/statistics")
    public ApiResponse<Map<String, Object>> getStatistics() {
        try {
            // 商品总数
            Integer productTotal = productService.getTotal(null, new Byte[]{0, 2});
            
            // 用户总数
            Integer userTotal = userService.getTotal(null);
            
            // 订单总数
            Integer orderTotal = productOrderService.getTotal(null, new Byte[]{3});
            
            // 分类总数
            Integer categoryTotal = categoryService.getTotal(null);
            
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("productTotal", productTotal);
            statistics.put("userTotal", userTotal);
            statistics.put("orderTotal", orderTotal);
            statistics.put("categoryTotal", categoryTotal);
            
            return ApiResponse.success(statistics);
            
        } catch (Exception e) {
            log.error("获取统计信息失败", e);
            throw new BusinessException(ApiResult.SYSTEM_ERROR, "获取统计信息失败");
        }
    }
}
