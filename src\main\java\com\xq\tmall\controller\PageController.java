package com.xq.tmall.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 页面跳转控制器
 * 专门处理页面跳转，不处理业务逻辑
 */
@Api(tags = "页面跳转")
@Controller
@RequiredArgsConstructor
public class PageController {
    
    // ==================== 前台页面 ====================
    
    /**
     * 首页
     */
    @ApiOperation("首页")
    @GetMapping("/")
    public String home() {
        return "fore/homePage";
    }
    
    /**
     * 登录页
     */
    @ApiOperation("登录页")
    @GetMapping("/login")
    public String login() {
        return "fore/loginPage";
    }
    
    /**
     * 注册页
     */
    @ApiOperation("注册页")
    @GetMapping("/register")
    public String register() {
        return "fore/register";
    }
    
    /**
     * 商品列表页
     */
    @ApiOperation("商品列表页")
    @GetMapping("/product")
    public String productList() {
        return "fore/productListPage";
    }
    
    /**
     * 商品详情页
     */
    @ApiOperation("商品详情页")
    @GetMapping("/product/{productId}")
    public String productDetails(@PathVariable Integer productId) {
        return "fore/productDetailsPage";
    }
    
    /**
     * 购物车页
     */
    @ApiOperation("购物车页")
    @GetMapping("/cart")
    public String cart() {
        return "fore/productBuyCarPage";
    }
    
    /**
     * 订单确认页
     */
    @ApiOperation("订单确认页")
    @GetMapping("/order/confirm")
    public String orderConfirm() {
        return "fore/orderConfirmPage";
    }
    
    /**
     * 订单列表页
     */
    @ApiOperation("订单列表页")
    @GetMapping("/order")
    public String orderList() {
        return "fore/orderListPage";
    }
    
    /**
     * 支付页
     */
    @ApiOperation("支付页")
    @GetMapping("/pay/{orderId}")
    public String pay(@PathVariable Integer orderId) {
        return "fore/productPayPage";
    }
    
    /**
     * 支付成功页
     */
    @ApiOperation("支付成功页")
    @GetMapping("/pay/success")
    public String paySuccess() {
        return "fore/productPaySuccessPage";
    }
    
    /**
     * 用户详情页
     */
    @ApiOperation("用户详情页")
    @GetMapping("/user/profile")
    public String userProfile() {
        return "fore/userDetails";
    }
    
    /**
     * 添加评价页
     */
    @ApiOperation("添加评价页")
    @GetMapping("/review/add")
    public String addReview() {
        return "fore/addReview";
    }
    
    // ==================== 后台管理页面 ====================
    
    /**
     * 管理员登录页
     */
    @ApiOperation("管理员登录页")
    @GetMapping("/admin")
    public String adminLogin() {
        return "admin/loginPage";
    }
    
    /**
     * 管理员首页
     */
    @ApiOperation("管理员首页")
    @GetMapping("/admin/home")
    public String adminHome() {
        return "admin/homePage";
    }
    
    /**
     * 分类管理页
     */
    @ApiOperation("分类管理页")
    @GetMapping("/admin/category")
    public String categoryManage() {
        return "admin/categoryManagePage";
    }
    
    /**
     * 商品管理页
     */
    @ApiOperation("商品管理页")
    @GetMapping("/admin/product")
    public String productManage() {
        return "admin/productManagePage";
    }
    
    /**
     * 属性管理页
     */
    @ApiOperation("属性管理页")
    @GetMapping("/admin/property")
    public String propertyManage() {
        return "admin/propertyManagePage";
    }
    
    /**
     * 用户管理页
     */
    @ApiOperation("用户管理页")
    @GetMapping("/admin/user")
    public String userManage() {
        return "admin/userManagePage";
    }
    
    /**
     * 订单管理页
     */
    @ApiOperation("订单管理页")
    @GetMapping("/admin/order")
    public String orderManage() {
        return "admin/orderManagePage";
    }
    
    /**
     * 评价管理页
     */
    @ApiOperation("评价管理页")
    @GetMapping("/admin/review")
    public String reviewManage() {
        return "admin/reviewManagePage";
    }
    
    /**
     * 账户管理页
     */
    @ApiOperation("账户管理页")
    @GetMapping("/admin/account")
    public String accountManage() {
        return "admin/accountManagePage";
    }
    
    // ==================== 错误页面 ====================
    
    /**
     * 404页面
     */
    @ApiOperation("404页面")
    @GetMapping("/404")
    public String notFound() {
        return "404";
    }
    
    /**
     * 错误页面
     */
    @ApiOperation("错误页面")
    @GetMapping("/error")
    public String error() {
        return "error";
    }
}
