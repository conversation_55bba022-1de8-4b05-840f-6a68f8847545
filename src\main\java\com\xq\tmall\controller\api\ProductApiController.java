package com.xq.tmall.controller.api;

import com.xq.tmall.common.ApiResponse;
import com.xq.tmall.common.ApiResult;
import com.xq.tmall.common.BusinessException;
import com.xq.tmall.entity.Category;
import com.xq.tmall.entity.Product;
import com.xq.tmall.entity.ProductImage;
import com.xq.tmall.entity.Review;
import com.xq.tmall.service.CategoryService;
import com.xq.tmall.service.ProductImageService;
import com.xq.tmall.service.ProductService;
import com.xq.tmall.service.ReviewService;
import com.xq.tmall.util.PageUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 商品API控制器
 */
@Slf4j
@Api(tags = "商品API")
@RestController
@RequestMapping("/api/public")
@RequiredArgsConstructor
public class ProductApiController {
    
    private final ProductService productService;
    private final CategoryService categoryService;
    private final ProductImageService productImageService;
    private final ReviewService reviewService;
    
    /**
     * 获取商品列表
     */
    @ApiOperation("获取商品列表")
    @GetMapping("/products")
    public ApiResponse<Map<String, Object>> getProductList(
            @ApiParam("分类ID") @RequestParam(required = false) Integer categoryId,
            @ApiParam("关键词") @RequestParam(required = false) String keyword,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam("每页数量") @RequestParam(defaultValue = "20") Integer size,
            @ApiParam("排序字段") @RequestParam(defaultValue = "product_id") String orderBy,
            @ApiParam("是否降序") @RequestParam(defaultValue = "true") Boolean desc) {
        
        try {
            // 构建查询条件
            Product queryProduct = new Product();
            if (categoryId != null) {
                Category category = new Category();
                category.setCategory_id(categoryId);
                queryProduct.setProduct_category(category);
            }
            if (keyword != null && !keyword.trim().isEmpty()) {
                queryProduct.setProduct_name(keyword.trim());
            }
            
            // 分页参数
            PageUtil pageUtil = new PageUtil((page - 1) * size, size);
            
            // 查询商品列表
            List<Product> productList = productService.getList(queryProduct, null, null, pageUtil);
            
            // 为每个商品设置图片
            for (Product product : productList) {
                ProductImage productImage = new ProductImage();
                productImage.setProductImage_product(product);
                productImage.setProductImage_type((byte) 0); // 主图
                List<ProductImage> imageList = productImageService.getList(productImage, null);
                product.setFirstProductImage(imageList.isEmpty() ? null : imageList.get(0));
                product.setProductImageList(imageList);
            }
            
            // 查询总数
            Integer total = productService.getTotal(queryProduct, null);
            
            // 构建响应数据
            Map<String, Object> result = new HashMap<>();
            result.put("list", productList);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
            result.put("totalPages", (int) Math.ceil((double) total / size));
            
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("获取商品列表失败", e);
            throw new BusinessException(ApiResult.SYSTEM_ERROR, "获取商品列表失败");
        }
    }
    
    /**
     * 获取商品详情
     */
    @ApiOperation("获取商品详情")
    @GetMapping("/products/{productId}")
    public ApiResponse<Product> getProductDetails(@PathVariable Integer productId) {
        try {
            Product product = productService.get(productId);
            if (product == null) {
                throw new BusinessException(ApiResult.PRODUCT_NOT_FOUND);
            }
            
            // 设置商品图片
            ProductImage productImage = new ProductImage();
            productImage.setProductImage_product(product);
            List<ProductImage> imageList = productImageService.getList(productImage, null);
            product.setProductImageList(imageList);
            if (!imageList.isEmpty()) {
                product.setFirstProductImage(imageList.get(0));
            }
            
            // 设置商品评价
            Review review = new Review();
            review.setReview_product(product);
            List<Review> reviewList = reviewService.getList(review, null);
            product.setReviewList(reviewList);
            
            return ApiResponse.success(product);
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取商品详情失败, productId: {}", productId, e);
            throw new BusinessException(ApiResult.SYSTEM_ERROR, "获取商品详情失败");
        }
    }
    
    /**
     * 搜索商品
     */
    @ApiOperation("搜索商品")
    @GetMapping("/products/search")
    public ApiResponse<Map<String, Object>> searchProducts(
            @ApiParam("搜索关键词") @RequestParam String keyword,
            @ApiParam("分类ID") @RequestParam(required = false) Integer categoryId,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam("每页数量") @RequestParam(defaultValue = "20") Integer size) {
        
        if (keyword == null || keyword.trim().isEmpty()) {
            throw new BusinessException(ApiResult.PARAM_ERROR, "搜索关键词不能为空");
        }
        
        try {
            // 构建查询条件
            Product queryProduct = new Product();
            queryProduct.setProduct_name(keyword.trim());
            
            if (categoryId != null) {
                Category category = new Category();
                category.setCategory_id(categoryId);
                queryProduct.setProduct_category(category);
            }
            
            // 分页参数
            PageUtil pageUtil = new PageUtil((page - 1) * size, size);
            
            // 搜索商品
            List<Product> productList = productService.getList(queryProduct, null, null, pageUtil);
            
            // 设置商品图片
            for (Product product : productList) {
                ProductImage productImage = new ProductImage();
                productImage.setProductImage_product(product);
                productImage.setProductImage_type((byte) 0);
                List<ProductImage> imageList = productImageService.getList(productImage, null);
                product.setFirstProductImage(imageList.isEmpty() ? null : imageList.get(0));
            }
            
            // 查询总数
            Integer total = productService.getTotal(queryProduct, null);
            
            // 构建响应数据
            Map<String, Object> result = new HashMap<>();
            result.put("list", productList);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
            result.put("keyword", keyword);
            result.put("totalPages", (int) Math.ceil((double) total / size));
            
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("搜索商品失败, keyword: {}", keyword, e);
            throw new BusinessException(ApiResult.SYSTEM_ERROR, "搜索商品失败");
        }
    }
    
    /**
     * 获取分类列表
     */
    @ApiOperation("获取分类列表")
    @GetMapping("/categories")
    public ApiResponse<List<Category>> getCategories() {
        try {
            List<Category> categoryList = categoryService.getList(null, null);
            return ApiResponse.success(categoryList);
        } catch (Exception e) {
            log.error("获取分类列表失败", e);
            throw new BusinessException(ApiResult.SYSTEM_ERROR, "获取分类列表失败");
        }
    }
    
    /**
     * 获取分类下的商品
     */
    @ApiOperation("获取分类下的商品")
    @GetMapping("/categories/{categoryId}/products")
    public ApiResponse<Map<String, Object>> getCategoryProducts(
            @PathVariable Integer categoryId,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam("每页数量") @RequestParam(defaultValue = "20") Integer size) {
        
        try {
            // 检查分类是否存在
            Category category = categoryService.get(categoryId);
            if (category == null) {
                throw new BusinessException(ApiResult.CATEGORY_NOT_FOUND);
            }
            
            // 构建查询条件
            Product queryProduct = new Product();
            queryProduct.setProduct_category(category);
            
            // 分页参数
            PageUtil pageUtil = new PageUtil((page - 1) * size, size);
            
            // 查询商品列表
            List<Product> productList = productService.getList(queryProduct, null, null, pageUtil);
            
            // 设置商品图片
            for (Product product : productList) {
                ProductImage productImage = new ProductImage();
                productImage.setProductImage_product(product);
                productImage.setProductImage_type((byte) 0);
                List<ProductImage> imageList = productImageService.getList(productImage, null);
                product.setFirstProductImage(imageList.isEmpty() ? null : imageList.get(0));
            }
            
            // 查询总数
            Integer total = productService.getTotal(queryProduct, null);
            
            // 构建响应数据
            Map<String, Object> result = new HashMap<>();
            result.put("category", category);
            result.put("list", productList);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
            result.put("totalPages", (int) Math.ceil((double) total / size));
            
            return ApiResponse.success(result);
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取分类商品失败, categoryId: {}", categoryId, e);
            throw new BusinessException(ApiResult.SYSTEM_ERROR, "获取分类商品失败");
        }
    }
}
