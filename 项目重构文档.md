# Tmall项目API重构文档

## 项目概述

本文档详细描述了将传统JSP+JSTL+Session架构的电商项目重构为现代化前后端分离架构的完整过程。

### 重构目标

1. **统一API格式**：实现RESTful风格的统一JSON返回格式
2. **前后端分离**：页面跳转与业务逻辑分离
3. **现代化认证**：使用JWT替代传统Session认证
4. **前端现代化**：JavaScript处理JSON数据替代JSTL渲染

## 重构前后对比

### 重构前架构
```
Controller -> JSP页面 -> JSTL渲染数据
     ↓
Session认证 -> HttpSession存储用户状态
```

### 重构后架构
```
PageController -> JSP页面 (纯展示)
     ↓
API Controller -> JSON数据 -> JavaScript渲染
     ↓
JWT认证 -> Token存储用户状态
```

## 详细重构步骤

### 第一阶段：基础架构搭建

#### 1. 统一响应格式
- **ApiResponse<T>**: 通用API响应包装类
- **ApiResult**: 响应状态码枚举
- **BusinessException**: 业务异常类
- **GlobalExceptionHandler**: 全局异常处理器

#### 2. JWT认证体系
- **JwtUtil**: JWT工具类，支持Token生成、解析、验证
- **JwtInterceptor**: JWT拦截器，自动验证Token
- **WebConfig**: Web配置类，注册拦截器和跨域配置

#### 3. 依赖更新
```xml
<!-- JWT依赖 -->
<dependency>
    <groupId>io.jsonwebtoken</groupId>
    <artifactId>jjwt-api</artifactId>
    <version>0.11.5</version>
</dependency>
```

### 第二阶段：API Controller重构

#### 1. 认证API (AuthApiController)
- `POST /api/auth/user/login` - 用户登录
- `POST /api/auth/admin/login` - 管理员登录
- `POST /api/auth/register` - 用户注册
- `GET /api/auth/captcha` - 获取验证码
- `POST /api/auth/refresh` - 刷新Token
- `POST /api/auth/logout` - 退出登录

#### 2. 商品API (ProductApiController)
- `GET /api/public/products` - 获取商品列表
- `GET /api/public/products/{id}` - 获取商品详情
- `GET /api/public/products/search` - 搜索商品
- `GET /api/public/categories` - 获取分类列表
- `GET /api/public/categories/{id}/products` - 获取分类商品

#### 3. 用户API (UserApiController)
- `GET /api/user/profile` - 获取用户信息
- `PUT /api/user/profile` - 更新用户信息
- `PUT /api/user/password` - 修改密码
- `POST /api/user/avatar` - 上传头像
- `GET /api/user/addresses` - 获取地址列表

#### 4. 订单API (OrderApiController)
- `GET /api/user/orders` - 获取订单列表
- `GET /api/user/orders/{id}` - 获取订单详情
- `POST /api/user/orders` - 创建订单
- `PUT /api/user/orders/{id}/cancel` - 取消订单
- `PUT /api/user/orders/{id}/confirm` - 确认收货
- `POST /api/user/orders/{id}/pay` - 支付订单

#### 5. 管理员API (AdminApiController)
- `GET /api/admin/categories` - 分类管理
- `GET /api/admin/products` - 商品管理
- `GET /api/admin/users` - 用户管理
- `GET /api/admin/orders` - 订单管理
- `GET /api/admin/statistics` - 统计信息

### 第三阶段：前端改造

#### 1. 页面跳转控制器 (PageController)
专门处理页面跳转，不包含业务逻辑：
```java
@GetMapping("/login")
public String login() {
    return "fore/loginPage";
}
```

#### 2. 前端API工具

**api-client.js**: 统一API客户端
- 自动Token管理
- 请求/响应拦截
- 错误处理
- Token自动刷新

**auth-manager.js**: 认证管理器
- 登录/注册处理
- 权限验证
- 用户状态管理
- UI状态更新

#### 3. JSP页面重构示例

**重构前** (使用JSTL):
```jsp
<c:if test="${user != null}">
    <span>欢迎，${user.username}</span>
</c:if>
```

**重构后** (使用JavaScript):
```javascript
// 页面加载时获取用户信息
if (authManager.isLoggedIn()) {
    const userInfo = authManager.getUserInfo();
    $('#username').text(userInfo.username);
}
```

### 第四阶段：认证系统迁移

#### 1. JWT Token结构
```json
{
  "userId": 123,
  "username": "user123",
  "type": "user",
  "iat": 1640995200,
  "exp": 1641600000
}
```

#### 2. 权限验证流程
1. 前端发送请求携带Token
2. JWT拦截器验证Token
3. 解析用户信息存入Request属性
4. Controller获取用户信息进行业务处理

#### 3. 前端Token管理
- localStorage存储Token
- 自动添加Authorization头
- Token过期自动刷新
- 登录状态实时更新

## 核心文件清单

### 后端新增文件
```
src/main/java/com/xq/tmall/
├── common/
│   ├── ApiResponse.java          # 统一响应格式
│   ├── ApiResult.java           # 响应状态枚举
│   ├── BusinessException.java   # 业务异常
│   └── GlobalExceptionHandler.java # 全局异常处理
├── config/
│   ├── JwtInterceptor.java      # JWT拦截器
│   └── WebConfig.java           # Web配置
├── controller/
│   ├── PageController.java      # 页面跳转控制器
│   └── api/
│       ├── AuthApiController.java    # 认证API
│       ├── ProductApiController.java # 商品API
│       ├── UserApiController.java    # 用户API
│       ├── OrderApiController.java   # 订单API
│       └── AdminApiController.java   # 管理员API
├── dto/
│   ├── LoginRequest.java        # 登录请求DTO
│   ├── LoginResponse.java       # 登录响应DTO
│   └── RegisterRequest.java     # 注册请求DTO
└── util/
    └── JwtUtil.java             # JWT工具类
```

### 前端新增文件
```
src/main/webapp/res/js/
├── api-client.js                # API客户端工具
├── auth-manager.js              # 认证管理器
└── fore/
    └── fore_login_new.js        # 重构后的登录页面JS
```

## 使用指南

### 1. API调用示例

**用户登录**:
```javascript
const result = await authManager.login('username', 'password', 'user');
if (result.success) {
    console.log('登录成功', result.userInfo);
}
```

**获取商品列表**:
```javascript
const response = await productAPI.getList({
    page: 1,
    size: 20,
    categoryId: 1
});
console.log('商品列表', response.data.list);
```

**创建订单**:
```javascript
const orderData = {
    productId: 123,
    quantity: 2,
    addressId: 456
};
const result = await orderAPI.create(orderData);
```

### 2. 权限控制

**前端权限检查**:
```javascript
// 检查是否登录
if (!authManager.isLoggedIn()) {
    window.location.href = '/login';
    return;
}

// 检查管理员权限
if (!authManager.isAdmin()) {
    alert('权限不足');
    return;
}
```

**后端权限验证**:
```java
@GetMapping("/api/user/profile")
public ApiResponse<User> getProfile(HttpServletRequest request) {
    Integer userId = (Integer) request.getAttribute("userId");
    // 业务逻辑...
}
```

### 3. 错误处理

**统一错误响应格式**:
```json
{
    "code": 401,
    "message": "Token已过期",
    "data": null,
    "timestamp": 1640995200000
}
```

**前端错误处理**:
```javascript
try {
    const response = await apiClient.get('/api/user/profile');
} catch (error) {
    console.error('请求失败:', error.message);
    // 显示错误提示
}
```

## 部署说明

### 1. 环境要求
- Java 8+
- Spring Boot 2.1.6
- MySQL 5.7+
- Maven 3.6+

### 2. 配置更新
无需额外配置，JWT密钥已内置在代码中。生产环境建议将密钥配置到环境变量。

### 3. 数据库兼容性
重构后完全兼容现有数据库结构，无需修改数据库。

## 测试建议

### 1. API测试
使用Postman或类似工具测试所有API接口：
- 认证接口测试
- 业务接口测试
- 权限验证测试
- 错误处理测试

### 2. 前端测试
- 页面功能测试
- 用户交互测试
- 权限控制测试
- 错误处理测试

### 3. 兼容性测试
- 新旧系统并存测试
- 数据一致性测试
- 性能对比测试

## 注意事项

1. **向后兼容**: 原有的Session认证方式仍然保留，可以逐步迁移
2. **安全性**: JWT Token包含敏感信息，注意HTTPS传输
3. **性能**: JWT验证比Session查询更高效
4. **扩展性**: 支持分布式部署，无需Session共享

## 总结

本次重构成功将传统MVC架构升级为现代化的前后端分离架构，主要收益：

1. **开发效率提升**: 前后端可并行开发
2. **维护性增强**: 职责分离，代码更清晰
3. **扩展性提升**: 支持移动端、小程序等多端接入
4. **性能优化**: 减少服务器渲染压力
5. **用户体验**: 页面响应更快，交互更流畅

重构后的系统具备了现代Web应用的特征，为后续功能扩展和技术升级奠定了良好基础。
