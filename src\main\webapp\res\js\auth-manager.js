/**
 * 认证管理器
 * 处理用户登录状态、权限验证等
 */
class AuthManager {
    constructor() {
        this.userInfo = this.getUserInfo();
        this.init();
    }

    /**
     * 初始化
     */
    init() {
        // 检查Token是否过期
        this.checkTokenExpiry();
        
        // 监听存储变化
        window.addEventListener('storage', (e) => {
            if (e.key === 'access_token' || e.key === 'user_info') {
                this.userInfo = this.getUserInfo();
                this.updateUI();
            }
        });
    }

    /**
     * 检查Token是否过期
     */
    checkTokenExpiry() {
        const token = localStorage.getItem('access_token');
        if (token) {
            try {
                const payload = JSON.parse(atob(token.split('.')[1]));
                const currentTime = Math.floor(Date.now() / 1000);
                
                if (payload.exp && payload.exp < currentTime) {
                    console.log('Token已过期，清除登录状态');
                    this.logout();
                }
            } catch (error) {
                console.error('Token解析失败:', error);
                this.logout();
            }
        }
    }

    /**
     * 登录
     */
    async login(username, password, userType = 'user', captcha = null, captchaId = null) {
        try {
            const loginData = {
                username: username,
                password: password
            };
            
            if (captcha && captchaId) {
                loginData.captcha = captcha;
                loginData.captchaId = captchaId;
            }

            let response;
            if (userType === 'admin') {
                response = await authAPI.adminLogin(loginData);
            } else {
                response = await authAPI.userLogin(loginData);
            }

            if (response.code === 200) {
                const { accessToken, refreshToken, userInfo } = response.data;
                
                // 保存Token和用户信息
                apiClient.setToken(accessToken, refreshToken);
                this.setUserInfo(userInfo);
                
                // 更新UI
                this.updateUI();
                
                return { success: true, userInfo: userInfo };
            } else {
                return { success: false, message: response.message };
            }
        } catch (error) {
            console.error('登录失败:', error);
            return { success: false, message: error.message || '登录失败' };
        }
    }

    /**
     * 注册
     */
    async register(userData) {
        try {
            const response = await authAPI.register(userData);
            
            if (response.code === 200) {
                return { success: true, message: response.message };
            } else {
                return { success: false, message: response.message };
            }
        } catch (error) {
            console.error('注册失败:', error);
            return { success: false, message: error.message || '注册失败' };
        }
    }

    /**
     * 退出登录
     */
    async logout() {
        try {
            // 调用后端退出接口
            await authAPI.logout();
        } catch (error) {
            console.error('退出登录失败:', error);
        } finally {
            // 清除本地数据
            apiClient.clearToken();
            this.clearUserInfo();
            this.updateUI();
            
            // 跳转到登录页
            if (this.isAdminPage()) {
                window.location.href = '/admin';
            } else {
                window.location.href = '/login';
            }
        }
    }

    /**
     * 检查是否已登录
     */
    isLoggedIn() {
        return !!apiClient.getToken() && !!this.userInfo;
    }

    /**
     * 检查是否为管理员
     */
    isAdmin() {
        return this.userInfo && this.userInfo.type === 'admin';
    }

    /**
     * 检查是否为用户
     */
    isUser() {
        return this.userInfo && this.userInfo.type === 'user';
    }

    /**
     * 检查是否为管理员页面
     */
    isAdminPage() {
        return window.location.pathname.startsWith('/admin');
    }

    /**
     * 获取用户信息
     */
    getUserInfo() {
        const userInfoStr = localStorage.getItem('user_info');
        return userInfoStr ? JSON.parse(userInfoStr) : null;
    }

    /**
     * 设置用户信息
     */
    setUserInfo(userInfo) {
        this.userInfo = userInfo;
        localStorage.setItem('user_info', JSON.stringify(userInfo));
    }

    /**
     * 清除用户信息
     */
    clearUserInfo() {
        this.userInfo = null;
        localStorage.removeItem('user_info');
    }

    /**
     * 权限检查
     */
    checkPermission(requiredType = null) {
        if (!this.isLoggedIn()) {
            this.redirectToLogin();
            return false;
        }

        if (requiredType && this.userInfo.type !== requiredType) {
            this.showPermissionDenied();
            return false;
        }

        return true;
    }

    /**
     * 跳转到登录页
     */
    redirectToLogin() {
        const currentPath = window.location.pathname;
        if (currentPath.startsWith('/admin')) {
            window.location.href = '/admin';
        } else {
            window.location.href = '/login';
        }
    }

    /**
     * 显示权限不足提示
     */
    showPermissionDenied() {
        alert('权限不足，无法访问该页面');
        history.back();
    }

    /**
     * 更新UI显示
     */
    updateUI() {
        // 更新登录状态显示
        const loginElements = document.querySelectorAll('.login-required');
        const logoutElements = document.querySelectorAll('.logout-required');
        const userElements = document.querySelectorAll('.user-only');
        const adminElements = document.querySelectorAll('.admin-only');

        if (this.isLoggedIn()) {
            // 显示需要登录的元素
            loginElements.forEach(el => el.style.display = '');
            // 隐藏需要未登录的元素
            logoutElements.forEach(el => el.style.display = 'none');

            // 根据用户类型显示/隐藏元素
            if (this.isUser()) {
                userElements.forEach(el => el.style.display = '');
                adminElements.forEach(el => el.style.display = 'none');
            } else if (this.isAdmin()) {
                userElements.forEach(el => el.style.display = 'none');
                adminElements.forEach(el => el.style.display = '');
            }

            // 更新用户信息显示
            this.updateUserDisplay();
        } else {
            // 隐藏需要登录的元素
            loginElements.forEach(el => el.style.display = 'none');
            // 显示需要未登录的元素
            logoutElements.forEach(el => el.style.display = '');
            // 隐藏用户和管理员专用元素
            userElements.forEach(el => el.style.display = 'none');
            adminElements.forEach(el => el.style.display = 'none');
        }
    }

    /**
     * 更新用户信息显示
     */
    updateUserDisplay() {
        if (this.userInfo) {
            // 更新用户名显示
            const usernameElements = document.querySelectorAll('.user-username');
            usernameElements.forEach(el => {
                el.textContent = this.userInfo.username || this.userInfo.nickname || '用户';
            });

            // 更新头像显示
            const avatarElements = document.querySelectorAll('.user-avatar');
            avatarElements.forEach(el => {
                if (this.userInfo.avatar) {
                    el.src = this.userInfo.avatar;
                }
            });
        }
    }

    /**
     * 获取验证码
     */
    async getCaptcha() {
        try {
            const response = await authAPI.getCaptcha();
            if (response.code === 200) {
                return response.data;
            } else {
                throw new Error(response.message);
            }
        } catch (error) {
            console.error('获取验证码失败:', error);
            throw error;
        }
    }
}

// 创建全局认证管理器实例
const authManager = new AuthManager();

// 页面加载完成后初始化UI
document.addEventListener('DOMContentLoaded', function() {
    authManager.updateUI();
    
    // 为退出登录按钮绑定事件
    const logoutButtons = document.querySelectorAll('.logout-btn');
    logoutButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            if (confirm('确定要退出登录吗？')) {
                authManager.logout();
            }
        });
    });
});

// 导出认证管理器
window.authManager = authManager;
