package com.xq.tmall.common;

import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    /**
     * 处理参数验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ApiResponse<String> handleValidationException(MethodArgumentNotValidException e) {
        log.error("参数验证异常: ", e);
        List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();
        StringBuilder errorMsg = new StringBuilder();
        for (FieldError error : fieldErrors) {
            errorMsg.append(error.getField()).append(": ").append(error.getDefaultMessage()).append("; ");
        }
        return ApiResponse.error(ApiResult.PARAM_ERROR.getCode(), errorMsg.toString());
    }
    
    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    public ApiResponse<String> handleBindException(BindException e) {
        log.error("绑定异常: ", e);
        List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();
        StringBuilder errorMsg = new StringBuilder();
        for (FieldError error : fieldErrors) {
            errorMsg.append(error.getField()).append(": ").append(error.getDefaultMessage()).append("; ");
        }
        return ApiResponse.error(ApiResult.PARAM_ERROR.getCode(), errorMsg.toString());
    }
    
    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public ApiResponse<String> handleBusinessException(BusinessException e) {
        log.error("业务异常: ", e);
        return ApiResponse.error(e.getCode(), e.getMessage());
    }
    
    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ApiResponse<String> handleIllegalArgumentException(IllegalArgumentException e) {
        log.error("非法参数异常: ", e);
        return ApiResponse.error(ApiResult.PARAM_INVALID.getCode(), e.getMessage());
    }
    
    /**
     * 处理空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    public ApiResponse<String> handleNullPointerException(NullPointerException e, HttpServletRequest request) {
        log.error("空指针异常, URL: {}", request.getRequestURL(), e);
        return ApiResponse.error(ApiResult.SYSTEM_ERROR.getCode(), "系统内部错误");
    }
    
    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public ApiResponse<String> handleRuntimeException(RuntimeException e, HttpServletRequest request) {
        log.error("运行时异常, URL: {}", request.getRequestURL(), e);
        return ApiResponse.error(ApiResult.SYSTEM_ERROR.getCode(), "系统运行异常");
    }
    
    /**
     * 处理其他异常
     */
    @ExceptionHandler(Exception.class)
    public ApiResponse<String> handleException(Exception e, HttpServletRequest request) {
        log.error("未知异常, URL: {}", request.getRequestURL(), e);
        return ApiResponse.error(ApiResult.SYSTEM_ERROR.getCode(), "系统异常，请联系管理员");
    }
}
