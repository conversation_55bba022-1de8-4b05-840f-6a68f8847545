/**
 * API客户端工具
 * 统一处理API请求和响应
 */
class ApiClient {
    constructor() {
        this.baseURL = '/api';
        this.token = localStorage.getItem('access_token');
        this.refreshToken = localStorage.getItem('refresh_token');
    }

    /**
     * 设置Token
     */
    setToken(token, refreshToken) {
        this.token = token;
        this.refreshToken = refreshToken;
        if (token) {
            localStorage.setItem('access_token', token);
        } else {
            localStorage.removeItem('access_token');
        }
        if (refreshToken) {
            localStorage.setItem('refresh_token', refreshToken);
        } else {
            localStorage.removeItem('refresh_token');
        }
    }

    /**
     * 获取Token
     */
    getToken() {
        return this.token || localStorage.getItem('access_token');
    }

    /**
     * 清除Token
     */
    clearToken() {
        this.token = null;
        this.refreshToken = null;
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('user_info');
    }

    /**
     * 获取请求头
     */
    getHeaders() {
        const headers = {
            'Content-Type': 'application/json'
        };
        const token = this.getToken();
        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }
        return headers;
    }

    /**
     * 处理响应
     */
    async handleResponse(response) {
        const data = await response.json();
        
        if (data.code === 200) {
            return data;
        } else if (data.code === 401) {
            // Token过期，尝试刷新
            if (this.refreshToken) {
                try {
                    const refreshResult = await this.refreshAccessToken();
                    if (refreshResult.success) {
                        // 重新发起原请求
                        return null; // 需要重试
                    }
                } catch (e) {
                    console.error('Token刷新失败:', e);
                }
            }
            // 跳转到登录页
            this.clearToken();
            window.location.href = '/login';
            throw new Error(data.message || '请先登录');
        } else {
            throw new Error(data.message || '请求失败');
        }
    }

    /**
     * 刷新Token
     */
    async refreshAccessToken() {
        try {
            const response = await fetch(`${this.baseURL}/auth/refresh`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    refreshToken: this.refreshToken
                })
            });

            const data = await response.json();
            if (data.code === 200) {
                this.setToken(data.data.accessToken, data.data.refreshToken);
                return { success: true };
            } else {
                this.clearToken();
                return { success: false };
            }
        } catch (error) {
            console.error('刷新Token失败:', error);
            this.clearToken();
            return { success: false };
        }
    }

    /**
     * 发送请求
     */
    async request(url, options = {}) {
        const config = {
            headers: this.getHeaders(),
            ...options
        };

        try {
            const response = await fetch(`${this.baseURL}${url}`, config);
            const result = await this.handleResponse(response);
            
            // 如果需要重试（Token刷新后）
            if (result === null && options.retry !== false) {
                config.headers = this.getHeaders();
                const retryResponse = await fetch(`${this.baseURL}${url}`, config);
                return await this.handleResponse(retryResponse);
            }
            
            return result;
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }

    /**
     * GET请求
     */
    async get(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        return this.request(fullUrl, { method: 'GET' });
    }

    /**
     * POST请求
     */
    async post(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    /**
     * PUT请求
     */
    async put(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    /**
     * DELETE请求
     */
    async delete(url) {
        return this.request(url, { method: 'DELETE' });
    }

    /**
     * 文件上传
     */
    async upload(url, formData) {
        const headers = {};
        const token = this.getToken();
        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }

        return this.request(url, {
            method: 'POST',
            headers: headers,
            body: formData
        });
    }
}

// 创建全局API客户端实例
const apiClient = new ApiClient();

/**
 * 认证相关API
 */
const authAPI = {
    // 用户登录
    userLogin: (data) => apiClient.post('/auth/user/login', data),
    
    // 管理员登录
    adminLogin: (data) => apiClient.post('/auth/admin/login', data),
    
    // 用户注册
    register: (data) => apiClient.post('/auth/register', data),
    
    // 获取验证码
    getCaptcha: () => apiClient.get('/auth/captcha'),
    
    // 退出登录
    logout: () => apiClient.post('/auth/logout'),
    
    // 刷新Token
    refreshToken: (refreshToken) => apiClient.post('/auth/refresh', { refreshToken })
};

/**
 * 用户相关API
 */
const userAPI = {
    // 获取用户信息
    getProfile: () => apiClient.get('/user/profile'),
    
    // 更新用户信息
    updateProfile: (data) => apiClient.put('/user/profile', data),
    
    // 获取用户地址
    getAddresses: () => apiClient.get('/user/addresses'),
    
    // 添加地址
    addAddress: (data) => apiClient.post('/user/addresses', data),
    
    // 更新地址
    updateAddress: (id, data) => apiClient.put(`/user/addresses/${id}`, data),
    
    // 删除地址
    deleteAddress: (id) => apiClient.delete(`/user/addresses/${id}`)
};

/**
 * 商品相关API
 */
const productAPI = {
    // 获取商品列表
    getList: (params) => apiClient.get('/public/products', params),
    
    // 获取商品详情
    getDetails: (id) => apiClient.get(`/public/products/${id}`),
    
    // 搜索商品
    search: (keyword, params) => apiClient.get('/public/products/search', { keyword, ...params }),
    
    // 获取分类列表
    getCategories: () => apiClient.get('/public/categories'),
    
    // 获取分类下的商品
    getCategoryProducts: (categoryId, params) => apiClient.get(`/public/categories/${categoryId}/products`, params)
};

/**
 * 订单相关API
 */
const orderAPI = {
    // 获取订单列表
    getList: (params) => apiClient.get('/user/orders', params),
    
    // 获取订单详情
    getDetails: (id) => apiClient.get(`/user/orders/${id}`),
    
    // 创建订单
    create: (data) => apiClient.post('/user/orders', data),
    
    // 取消订单
    cancel: (id) => apiClient.put(`/user/orders/${id}/cancel`),
    
    // 确认收货
    confirm: (id) => apiClient.put(`/user/orders/${id}/confirm`),
    
    // 支付订单
    pay: (id, data) => apiClient.post(`/user/orders/${id}/pay`, data)
};

/**
 * 购物车相关API
 */
const cartAPI = {
    // 获取购物车
    getCart: () => apiClient.get('/user/cart'),
    
    // 添加到购物车
    addItem: (data) => apiClient.post('/user/cart/items', data),
    
    // 更新购物车项
    updateItem: (id, data) => apiClient.put(`/user/cart/items/${id}`, data),
    
    // 删除购物车项
    removeItem: (id) => apiClient.delete(`/user/cart/items/${id}`),
    
    // 清空购物车
    clear: () => apiClient.delete('/user/cart')
};

// 导出API对象
window.apiClient = apiClient;
window.authAPI = authAPI;
window.userAPI = userAPI;
window.productAPI = productAPI;
window.orderAPI = orderAPI;
window.cartAPI = cartAPI;
